import { prisma } from "@/lib/prisma"


async function fixStudentSchedule() {
  console.log('🔧 Fixing student schedule functionality...')

  try {
    // Get or create a test student
    let student = await prisma.user.findFirst({
      where: { role: 'STUDENT' }
    })

    if (!student) {
      student = await prisma.user.create({
        data: {
          name: 'Test Student',
          email: '<EMAIL>',
          role: 'STUDENT'
        }
      })
      console.log('✅ Created test student')
    }

    // Get or create admin user
    let admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!admin) {
      admin = await prisma.user.create({
        data: {
          name: 'Test Admin',
          email: '<EMAIL>',
          role: 'ADMIN'
        }
      })
      console.log('✅ Created test admin')
    }

    // Create some scheduled quizzes with startTime and endTime
    const now = new Date()
    const scheduledQuizData = [
      {
        title: "JavaScript Fundamentals Test",
        description: "Test your knowledge of JavaScript basics",
        type: "TEST_SERIES" as const,
        difficulty: "MEDIUM" as const,
        startTime: new Date(now.getTime() + 2 * 60 * 60 * 1000), // 2 hours from now
        endTime: new Date(now.getTime() + 4 * 60 * 60 * 1000), // 4 hours from now
        timeLimit: 90,
        maxAttempts: 1
      },
      {
        title: "React Components Quiz",
        description: "Test your React knowledge",
        type: "QUIZ" as const,
        difficulty: "HARD" as const,
        startTime: new Date(now.getTime() + 24 * 60 * 60 * 1000), // 1 day from now
        endTime: new Date(now.getTime() + 26 * 60 * 60 * 1000), // 1 day + 2 hours from now
        timeLimit: 45,
        maxAttempts: 2
      }
    ]

    // Create scheduled quizzes and enroll student
    for (const quizData of scheduledQuizData) {
      // Check if quiz already exists
      let quiz = await prisma.quiz.findFirst({
        where: { title: quizData.title }
      })

      if (!quiz) {
        quiz = await prisma.quiz.create({
          data: {
            title: quizData.title,
            description: quizData.description,
            type: quizData.type,
            difficulty: quizData.difficulty,
            startTime: quizData.startTime,
            endTime: quizData.endTime,
            timeLimit: quizData.timeLimit,
            maxAttempts: quizData.maxAttempts,
            isPublished: true,
            createdBy: admin.id
          }
        })

        // Create a sample question
        await prisma.question.create({
          data: {
            quizId: quiz.id,
            type: 'MCQ',
            text: `Sample question for ${quiz.title}`,
            options: ['Option A', 'Option B', 'Option C', 'Option D'],
            correctAnswer: 'Option A',
            explanation: 'This is a sample question.',
            points: 10,
            order: 1
          }
        })

        console.log(`✅ Created scheduled quiz: ${quiz.title}`)
      }

      // Enroll student in the quiz
      const existingEnrollment = await prisma.quizEnrollment.findFirst({
        where: {
          quizId: quiz.id,
          userId: student.id
        }
      })

      if (!existingEnrollment) {
        await prisma.quizEnrollment.create({
          data: {
            quizId: quiz.id,
            userId: student.id
          }
        })
        console.log(`✅ Enrolled student in: ${quiz.title}`)
      }
    }

    // Create some ScheduledQuiz entries
    const scheduledQuizEntries = [
      {
        title: "Weekly Math Challenge",
        description: "Weekly mathematics challenge for all levels",
        startTime: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        endTime: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000), // 3 hours duration
        duration: 120,
        maxAttempts: 1
      }
    ]

    // Create a base quiz for ScheduledQuiz entries
    let baseQuiz = await prisma.quiz.findFirst({
      where: { title: "Base Math Quiz" }
    })

    if (!baseQuiz) {
      baseQuiz = await prisma.quiz.create({
        data: {
          title: "Base Math Quiz",
          description: "Base quiz for scheduled entries",
          type: "QUIZ",
          difficulty: "MEDIUM",
          timeLimit: 60,
          maxAttempts: 1,
          isPublished: true,
          createdBy: admin.id
        }
      })

      // Create a sample question
      await prisma.question.create({
        data: {
          quizId: baseQuiz.id,
          type: 'MCQ',
          text: 'What is 2 + 2?',
          options: ['3', '4', '5', '6'],
          correctAnswer: '4',
          explanation: '2 + 2 = 4',
          points: 10,
          order: 1
        }
      })
    }

    // Create ScheduledQuiz entries
    for (const scheduleData of scheduledQuizEntries) {
      const existingScheduled = await prisma.scheduledQuiz.findFirst({
        where: { title: scheduleData.title }
      })

      if (!existingScheduled) {
        const scheduledQuiz = await prisma.scheduledQuiz.create({
          data: {
            quizId: baseQuiz.id,
            title: scheduleData.title,
            description: scheduleData.description,
            startTime: scheduleData.startTime,
            endTime: scheduleData.endTime,
            duration: scheduleData.duration,
            maxAttempts: scheduleData.maxAttempts,
            createdBy: admin.id
          }
        })

        // Enroll student in the scheduled quiz
        await prisma.quizEnrollment.create({
          data: {
            quizId: baseQuiz.id,
            userId: student.id,
            scheduledQuizId: scheduledQuiz.id
          }
        })

        console.log(`✅ Created ScheduledQuiz entry: ${scheduleData.title}`)
      }
    }

    console.log('🎉 Student schedule fix completed!')
    console.log(`👤 Test student: ${student.name} (${student.email})`)
    console.log('📅 Students should now see scheduled quizzes in their schedule page')

  } catch (error) {
    console.error('❌ Error fixing student schedule:', error)
  }
}

fixStudentSchedule()
