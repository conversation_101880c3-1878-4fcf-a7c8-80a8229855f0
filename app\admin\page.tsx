"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Shield,
  Users,
  FileText,
  BarChart3,
  Settings,
  TrendingUp,
  Clock,
  Award,
  Activity,
  Plus,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  AlertCircle,
  Bell,
  Calendar,
  Sparkles,
  Zap,
  Target,
  Globe
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"

interface DashboardStats {
  totalUsers: number
  activeUsers: number
  totalQuizzes: number
  totalAttempts: number
  averageScore: number
  recentActivity: Array<{
    id: string
    type: 'quiz_created' | 'quiz_attempt' | 'user_registered'
    title: string
    description: string
    timestamp: string
    user?: {
      name: string
      email: string
    }
  }>
  trends: {
    usersGrowth: number
    quizzesGrowth: number
    attemptsGrowth: number
    scoreImprovement: number
  }
  performance: {
    quizCompletionRate: number
    userEngagementRate: number
  }
  systemStatus: {
    serverStatus: 'online' | 'offline' | 'maintenance'
    databaseStatus: 'healthy' | 'warning' | 'error'
    aiServiceStatus: 'active' | 'inactive' | 'error'
    storageUsed: number
    lastBackup: string
  }
}

export default function AdminDashboard() {
  const { data: session } = useSession()
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/dashboard/stats')

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard stats')
      }

      const data = await response.json()
      setDashboardStats(data.data)
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      toast.error('Failed to load dashboard statistics')
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchDashboardStats()
    setRefreshing(false)
    toast.success('Dashboard refreshed')
  }

  // Create stats array from API data
  const stats = dashboardStats ? [
    {
      title: "Total Users",
      value: dashboardStats.totalUsers.toLocaleString(),
      icon: Users,
      change: `${dashboardStats.trends.usersGrowth >= 0 ? '+' : ''}${dashboardStats.trends.usersGrowth}%`,
      trend: dashboardStats.trends.usersGrowth >= 0 ? "up" : "down"
    },
    {
      title: "Active Quizzes",
      value: dashboardStats.totalQuizzes.toLocaleString(),
      icon: FileText,
      change: `${dashboardStats.trends.quizzesGrowth >= 0 ? '+' : ''}${dashboardStats.trends.quizzesGrowth}%`,
      trend: dashboardStats.trends.quizzesGrowth >= 0 ? "up" : "down"
    },
    {
      title: "Quiz Attempts",
      value: dashboardStats.totalAttempts.toLocaleString(),
      icon: Activity,
      change: `${dashboardStats.trends.attemptsGrowth >= 0 ? '+' : ''}${dashboardStats.trends.attemptsGrowth}%`,
      trend: dashboardStats.trends.attemptsGrowth >= 0 ? "up" : "down"
    },
    {
      title: "Avg. Score",
      value: `${dashboardStats.averageScore}%`,
      icon: TrendingUp,
      change: `${dashboardStats.trends.scoreImprovement >= 0 ? '+' : ''}${dashboardStats.trends.scoreImprovement}%`,
      trend: dashboardStats.trends.scoreImprovement >= 0 ? "up" : "down"
    },
  ] : []

  const quickActions = [
    { title: "Create Quiz", description: "Generate a new quiz", icon: Plus, href: "/admin/quizzes/create", color: "bg-blue-500" },
    { title: "Live Quiz", description: "Manage live sessions", icon: Zap, href: "/admin/live-quiz", color: "bg-yellow-500" },
    { title: "Manage Users", description: "View and edit users", icon: Users, href: "/admin/users", color: "bg-green-500" },
    { title: "View Analytics", description: "Check performance", icon: BarChart3, href: "/admin/analytics-dashboard", color: "bg-purple-500" },
    { title: "System Settings", description: "Configure platform", icon: Settings, href: "/admin/settings", color: "bg-orange-500" },
  ]

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!dashboardStats) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 mx-auto mb-4 text-destructive" />
            <p className="text-muted-foreground mb-4">Failed to load dashboard data</p>
            <Button onClick={fetchDashboardStats}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      <div className="p-6 space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="relative"
        >
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

          <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                    <BarChart3 className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Dashboard Overview
                    </h1>
                    <p className="text-muted-foreground text-lg">
                      Welcome back, {session?.user?.name || 'Admin'}! Here's what's happening today.
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className="glass hover:bg-white/20 dark:hover:bg-gray-800/20 border-white/20 dark:border-gray-700/20"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                <Badge className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 shadow-lg">
                  <Shield className="h-4 w-4 mr-2" />
                  Admin Access
                </Badge>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -5, scale: 1.02 }}
              className="group"
            >
              <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden relative">
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </CardTitle>
                  <div className="p-3 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-xl backdrop-blur-sm border border-white/20 dark:border-gray-700/20 group-hover:scale-110 transition-transform duration-300">
                    <stat.icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                </CardHeader>
                <CardContent className="relative z-10">
                  <div className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                    {stat.value}
                  </div>
                  <p className="text-sm text-muted-foreground flex items-center gap-2 mt-2">
                    <div className={`p-1 rounded-full ${stat.trend === 'up' ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'}`}>
                      <TrendingUp className={`h-3 w-3 ${stat.trend === 'up' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400 rotate-180'}`} />
                    </div>
                    <span className={stat.trend === 'up' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                      {stat.change}
                    </span>
                    <span>from last month</span>
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="lg:col-span-2"
          >
            <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl">
              <CardHeader className="pb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-purple-500/20 to-pink-600/20 rounded-lg backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                    <Zap className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold">Quick Actions</CardTitle>
                    <CardDescription className="text-base">
                      Common administrative tasks and shortcuts
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {quickActions.map((action, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        variant="outline"
                        className="h-24 w-full flex flex-col gap-3 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 hover:bg-white/60 dark:hover:bg-gray-800/60 transition-all duration-300 group"
                        asChild
                      >
                        <a href={action.href}>
                          <div className={`p-3 rounded-xl ${action.color} text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                            <action.icon className="h-5 w-5" />
                          </div>
                          <div className="text-center">
                            <div className="font-semibold text-sm">{action.title}</div>
                            <div className="text-xs text-muted-foreground">{action.description}</div>
                          </div>
                        </a>
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl h-full">
              <CardHeader className="pb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-green-500/20 to-blue-600/20 rounded-lg backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                    <Activity className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold">Recent Activity</CardTitle>
                    <CardDescription className="text-base">
                      Latest system activities and updates
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-h-96 overflow-y-auto custom-scrollbar">
                  {dashboardStats.recentActivity.length > 0 ? (
                    dashboardStats.recentActivity.map((activity, index) => (
                      <motion.div
                        key={activity.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
                        className="flex items-start gap-4 p-3 rounded-xl glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border border-white/20 dark:border-gray-700/20 hover:bg-white/60 dark:hover:bg-gray-800/60 transition-all duration-300"
                      >
                        <div className="flex-shrink-0">
                          {activity.type === 'quiz_created' && (
                            <div className="w-10 h-10 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                              <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                            </div>
                          )}
                          {activity.type === 'quiz_attempt' && (
                            <div className="w-10 h-10 bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                              <Target className="h-5 w-5 text-green-600 dark:text-green-400" />
                            </div>
                          )}
                          {activity.type === 'user_registered' && (
                            <div className="w-10 h-10 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                              <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-semibold text-gray-900 dark:text-white">{activity.title}</p>
                          <p className="text-xs text-muted-foreground truncate mt-1">{activity.description}</p>
                          <p className="text-xs text-muted-foreground mt-2 flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {new Date(activity.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </motion.div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Activity className="h-8 w-8 text-gray-400" />
                      </div>
                      <p className="text-sm text-muted-foreground">No recent activity</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Additional Insights */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Performance Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl">
              <CardHeader className="pb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-orange-500/20 to-red-600/20 rounded-lg backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                    <BarChart3 className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold">Performance Overview</CardTitle>
                    <CardDescription className="text-base">
                      Key metrics and performance indicators
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {dashboardStats ? (
                  <div className="space-y-6">
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.7 }}
                      className="space-y-3"
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-semibold">Quiz Completion Rate</span>
                        <span className="text-sm font-bold text-blue-600 dark:text-blue-400">{dashboardStats.performance.quizCompletionRate}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                        <motion.div
                          className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full shadow-sm"
                          initial={{ width: 0 }}
                          animate={{ width: `${dashboardStats.performance.quizCompletionRate}%` }}
                          transition={{ duration: 1, delay: 0.8 }}
                        />
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.8 }}
                      className="space-y-3"
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-semibold">Average Score</span>
                        <span className="text-sm font-bold text-green-600 dark:text-green-400">{dashboardStats.averageScore}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                        <motion.div
                          className="bg-gradient-to-r from-green-500 to-green-600 h-3 rounded-full shadow-sm"
                          initial={{ width: 0 }}
                          animate={{ width: `${dashboardStats.averageScore}%` }}
                          transition={{ duration: 1, delay: 0.9 }}
                        />
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.9 }}
                      className="space-y-3"
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-semibold">User Engagement</span>
                        <span className="text-sm font-bold text-purple-600 dark:text-purple-400">{dashboardStats.performance.userEngagementRate}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                        <motion.div
                          className="bg-gradient-to-r from-purple-500 to-purple-600 h-3 rounded-full shadow-sm"
                          initial={{ width: 0 }}
                          animate={{ width: `${dashboardStats.performance.userEngagementRate}%` }}
                          transition={{ duration: 1, delay: 1.0 }}
                        />
                      </div>
                    </motion.div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="space-y-3 animate-pulse">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* System Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl">
              <CardHeader className="pb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-emerald-500/20 to-teal-600/20 rounded-lg backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                    <Globe className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold">System Status</CardTitle>
                    <CardDescription className="text-base">
                      Current system health and status
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {dashboardStats ? (
                  <div className="space-y-4">
                    {[
                      { label: 'Server Status', value: dashboardStats.systemStatus.serverStatus, key: 'server' },
                      { label: 'Database', value: dashboardStats.systemStatus.databaseStatus, key: 'database' },
                      { label: 'AI Service', value: dashboardStats.systemStatus.aiServiceStatus, key: 'ai' }
                    ].map((item, index) => (
                      <motion.div
                        key={item.key}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: 0.8 + index * 0.1 }}
                        className="flex items-center justify-between p-3 rounded-xl glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border border-white/20 dark:border-gray-700/20"
                      >
                        <span className="text-sm font-semibold">{item.label}</span>
                        <Badge className={`
                          ${item.value === 'online' || item.value === 'healthy' || item.value === 'active'
                            ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white border-0 shadow-lg"
                            : item.value === 'maintenance' || item.value === 'warning' || item.value === 'inactive'
                            ? "bg-gradient-to-r from-yellow-500 to-orange-600 text-white border-0 shadow-lg"
                            : "bg-gradient-to-r from-red-500 to-pink-600 text-white border-0 shadow-lg"
                          }
                        `}>
                          <div className="w-2 h-2 rounded-full bg-white/30 mr-2" />
                          {item.value.charAt(0).toUpperCase() + item.value.slice(1)}
                        </Badge>
                      </motion.div>
                    ))}

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 1.1 }}
                      className="flex items-center justify-between p-3 rounded-xl glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border border-white/20 dark:border-gray-700/20"
                    >
                      <span className="text-sm font-semibold">Storage</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                            style={{ width: `${dashboardStats.systemStatus.storageUsed}%` }}
                          />
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {dashboardStats.systemStatus.storageUsed}%
                        </Badge>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 1.2 }}
                      className="flex items-center justify-between p-3 rounded-xl glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border border-white/20 dark:border-gray-700/20"
                    >
                      <span className="text-sm font-semibold">Last Backup</span>
                      <span className="text-sm text-muted-foreground font-medium">
                        {new Date(dashboardStats.systemStatus.lastBackup).toLocaleString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          hour: 'numeric',
                          minute: '2-digit',
                          hour12: true
                        })}
                      </span>
                    </motion.div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center justify-between p-3 rounded-xl glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border border-white/20 dark:border-gray-700/20 animate-pulse">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
                        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
