import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  status: z.enum(['WAITING', 'ACTIVE']).optional(),
  search: z.string().optional(),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']).optional(),
  category: z.string().optional(),
  joinable: z.enum(['true', 'false']).optional().default('true')
})

// GET /api/student/live-quiz/available - Get available live quiz sessions for students
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    const { page, limit, status, search, difficulty, category, joinable } = validatedQuery

    try {
      // Build where clause
      const where: any = {
        // Only show sessions that are waiting or active
        status: status ? status : { in: ['WAITING', 'ACTIVE'] }
      }

      // If joinable filter is true, add additional constraints
      if (joinable === 'true') {
        where.AND = [
          // Session allows late join OR is still waiting
          {
            OR: [
              { allowLateJoin: true },
              { status: 'WAITING' }
            ]
          },
          // Quiz is published
          { quiz: { isPublished: true } }
        ]
      }

      // Add search filter
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { quiz: { title: { contains: search, mode: 'insensitive' } } },
          { quiz: { description: { contains: search, mode: 'insensitive' } } }
        ]
      }

      // Add difficulty filter
      if (difficulty) {
        where.quiz = {
          ...where.quiz,
          difficulty
        }
      }

      // Add category filter
      if (category) {
        where.quiz = {
          ...where.quiz,
          category
        }
      }

      // Get total count
      const totalCount = await prisma.liveQuizSession.count({ where })

      // Get sessions with pagination
      const sessions = await prisma.liveQuizSession.findMany({
        where,
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              description: true,
              difficulty: true,
              timeLimit: true,
              category: true,
              thumbnail: true,
              tags: true,
              questions: {
                select: { id: true }
              }
            }
          },
          creator: {
            select: {
              id: true,
              name: true
            }
          },
          participants: {
            where: { isActive: true },
            select: {
              id: true,
              userId: true,
              user: {
                select: {
                  name: true
                }
              }
            }
          },
          _count: {
            select: {
              participants: {
                where: { isActive: true }
              }
            }
          }
        },
        orderBy: [
          { status: 'asc' }, // WAITING first, then ACTIVE
          { scheduledStart: 'asc' },
          { createdAt: 'desc' }
        ],
        skip: (page - 1) * limit,
        take: limit
      })

      // Check if user is already participating in each session
      const userParticipations = await prisma.liveQuizParticipant.findMany({
        where: {
          userId: user.id,
          sessionId: { in: sessions.map(s => s.id) },
          isActive: true
        },
        select: { sessionId: true }
      })

      const userSessionIds = new Set(userParticipations.map(p => p.sessionId))

      const totalPages = Math.ceil(totalCount / limit)

      return APIResponse.success({
        sessions: sessions.map(session => {
          const isParticipating = userSessionIds.has(session.id)
          const participantCount = session._count.participants
          const canJoin = !isParticipating && 
                         (session.status === 'WAITING' || 
                          (session.status === 'ACTIVE' && session.allowLateJoin)) &&
                         (!session.maxParticipants || participantCount < session.maxParticipants)

          return {
            id: session.id,
            title: session.title,
            description: session.description,
            status: session.status,
            maxParticipants: session.maxParticipants,
            currentQuestion: session.currentQuestion,
            questionTimeLimit: session.questionTimeLimit,
            autoAdvance: session.autoAdvance,
            showLeaderboard: session.showLeaderboard,
            allowLateJoin: session.allowLateJoin,
            startTime: session.startTime,
            scheduledStart: session.scheduledStart,
            createdAt: session.createdAt,
            quiz: {
              ...session.quiz,
              questionCount: session.quiz.questions.length
            },
            creator: session.creator,
            participantCount,
            isParticipating,
            canJoin,
            participants: session.participants.map(p => ({
              id: p.id,
              userName: p.user.name
            }))
          }
        }),
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }, 'Available live quiz sessions retrieved successfully')

    } catch (error) {
      console.error('Error fetching available live quiz sessions:', error)
      return APIResponse.error('Failed to fetch available live quiz sessions', 500)
    }
  }
)
