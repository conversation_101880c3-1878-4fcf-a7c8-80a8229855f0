import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { getSocketClient } from '@/lib/socket-client'
import { liveQuizDelivery } from '@/lib/live-quiz-delivery'
import { z } from 'zod'

const questionControlSchema = z.object({
  action: z.enum(['next', 'previous', 'goto']),
  questionIndex: z.number().min(0).optional() // Required for 'goto' action
})

// POST /api/admin/live-quiz/sessions/[id]/question - Control question progression
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: questionControlSchema
  },
  async (request: NextRequest, { params, validatedBody, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string
    const { action, questionIndex } = validatedBody

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Get session with quiz questions
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            include: {
              questions: {
                select: {
                  id: true,
                  type: true,
                  text: true,
                  options: true,
                  correctAnswer: true,
                  explanation: true,
                  points: true,
                  order: true
                },
                orderBy: { order: 'asc' }
              }
            }
          },
          participants: {
            where: { isActive: true },
            include: {
              user: {
                select: { id: true, name: true }
              }
            }
          }
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Check if session is active
      if (session.status !== 'ACTIVE') {
        return APIResponse.error('Session must be active to control questions', 400, 'SESSION_NOT_ACTIVE')
      }

      const totalQuestions = session.quiz.questions.length
      let newQuestionIndex = session.currentQuestion

      // Calculate new question index based on action
      switch (action) {
        case 'next':
          if (session.currentQuestion >= totalQuestions - 1) {
            return APIResponse.error('Already at the last question', 400, 'LAST_QUESTION')
          }
          newQuestionIndex = session.currentQuestion + 1
          break

        case 'previous':
          if (session.currentQuestion <= 0) {
            return APIResponse.error('Already at the first question', 400, 'FIRST_QUESTION')
          }
          newQuestionIndex = session.currentQuestion - 1
          break

        case 'goto':
          if (questionIndex === undefined) {
            return APIResponse.error('Question index is required for goto action', 400, 'MISSING_QUESTION_INDEX')
          }
          if (questionIndex < 0 || questionIndex >= totalQuestions) {
            return APIResponse.error('Invalid question index', 400, 'INVALID_QUESTION_INDEX')
          }
          newQuestionIndex = questionIndex
          break

        default:
          return APIResponse.error('Invalid action', 400, 'INVALID_ACTION')
      }

      // Update session current question
      const updatedSession = await prisma.liveQuizSession.update({
        where: { id: sessionId },
        data: {
          currentQuestion: newQuestionIndex
        },
        include: {
          quiz: {
            include: {
              questions: {
                select: {
                  id: true,
                  type: true,
                  text: true,
                  options: true,
                  correctAnswer: true,
                  explanation: true,
                  points: true,
                  order: true
                },
                orderBy: { order: 'asc' }
              }
            }
          }
        }
      })

      const currentQuestion = updatedSession.quiz.questions[newQuestionIndex]

      // Calculate question statistics from participants
      const participantAnswers = await prisma.liveQuizParticipant.findMany({
        where: { 
          sessionId,
          isActive: true
        },
        select: {
          answers: true,
          currentQuestion: true
        }
      })

      // Count how many participants have answered the current question
      const questionStats = {
        totalParticipants: participantAnswers.length,
        answeredCount: 0,
        correctCount: 0,
        answerDistribution: {} as Record<string, number>
      }

      if (currentQuestion) {
        for (const participant of participantAnswers) {
          const answers = participant.answers as Record<string, any>
          const answer = answers[currentQuestion.id]
          
          if (answer) {
            questionStats.answeredCount++
            
            // Count correct answers
            if (answer === currentQuestion.correctAnswer) {
              questionStats.correctCount++
            }
            
            // Track answer distribution
            const answerKey = String(answer)
            questionStats.answerDistribution[answerKey] = (questionStats.answerDistribution[answerKey] || 0) + 1
          }
        }
      }

      // Use the delivery service for synchronized question delivery
      try {
        await liveQuizDelivery.startQuestionDelivery({
          sessionId,
          questionIndex: newQuestionIndex,
          timeLimit: updatedSession.questionTimeLimit || undefined,
          autoAdvance: updatedSession.autoAdvance
        })

        // Also broadcast via socket for immediate UI updates
        const socketClient = getSocketClient()

        socketClient.emit('live-quiz:question-changed', {
          sessionId,
          session: {
            id: updatedSession.id,
            title: updatedSession.title,
            currentQuestion: newQuestionIndex,
            totalQuestions
          },
          questionData: currentQuestion,
          questionStats,
          previousQuestion: action === 'next' ? session.quiz.questions[session.currentQuestion] : null
        })

        // Send notifications to participants
        for (const participant of session.participants) {
          socketClient.emit('notification:received', {
            userId: participant.userId,
            type: 'info',
            title: 'New Question',
            message: `Question ${newQuestionIndex + 1} of ${totalQuestions}`,
            data: {
              sessionId,
              action: 'question-changed',
              questionIndex: newQuestionIndex
            }
          })
        }
      } catch (socketError) {
        console.warn('Failed to send socket notifications:', socketError)
        // Continue execution even if socket notifications fail
      }

      return APIResponse.success({
        session: {
          id: updatedSession.id,
          title: updatedSession.title,
          currentQuestion: newQuestionIndex,
          totalQuestions,
          status: updatedSession.status
        },
        currentQuestionData: currentQuestion,
        questionStats,
        progress: {
          current: newQuestionIndex + 1,
          total: totalQuestions,
          percentage: Math.round(((newQuestionIndex + 1) / totalQuestions) * 100)
        }
      }, `Question ${action} completed successfully`)

    } catch (error) {
      console.error('Error controlling question:', error)
      return APIResponse.error('Failed to control question', 500)
    }
  }
)
