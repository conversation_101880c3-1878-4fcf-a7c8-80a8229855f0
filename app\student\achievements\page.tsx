"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Award, 
  Trophy, 
  Star, 
  Search,
  Filter,
  Lock,
  CheckCircle,
  Calendar,
  Target,
  Zap,
  BookOpen,
  Users,
  Clock,
  TrendingUp,
  Crown,
  Gem,
  Shield
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"

interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  category: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  points: number
  isUnlocked: boolean
  unlockedAt?: string
  progress?: {
    current: number
    required: number
  }
  requirements: string[]
}

interface AchievementStats {
  totalAchievements: number
  unlockedAchievements: number
  totalPoints: number
  earnedPoints: number
  completionPercentage: number
  rank: number
  totalUsers: number
}

export default function StudentAchievements() {
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [stats, setStats] = useState<AchievementStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedRarity, setSelectedRarity] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [showFilters, setShowFilters] = useState(false)

  const categories = ["Learning", "Performance", "Consistency", "Social", "Special"]

  // Helper function to get points based on rarity
  const getRarityPoints = (rarity: string): number => {
    switch (rarity) {
      case 'common': return 10
      case 'uncommon': return 25
      case 'rare': return 50
      case 'epic': return 100
      case 'legendary': return 200
      default: return 10
    }
  }

  useEffect(() => {
    fetchAchievements()
  }, [])

  const fetchAchievements = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/student/achievements')

      if (!response.ok) {
        throw new Error('Failed to fetch achievements')
      }

      const data = await response.json()

      if (data.success) {
        // Handle case where data might be undefined or null
        const earnedAchievements = data.data?.earned || []
        const availableAchievements = data.data?.available || []

        // Transform API data to match frontend interface
        const allAchievements: Achievement[] = [
          ...earnedAchievements.map((achievement: any) => ({
            id: achievement.id,
            title: achievement.title,
            description: achievement.description,
            icon: achievement.icon,
            category: 'Learning', // Default category since API doesn't have this
            rarity: achievement.rarity,
            points: getRarityPoints(achievement.rarity),
            isUnlocked: true,
            unlockedAt: achievement.unlockedAt,
            requirements: [achievement.description],
            progress: { current: 100, required: 100 }
          })),
          ...availableAchievements.map((achievement: any) => ({
            id: achievement.id,
            title: achievement.title,
            description: achievement.description,
            icon: achievement.icon,
            category: 'Learning', // Default category since API doesn't have this
            rarity: achievement.rarity,
            points: getRarityPoints(achievement.rarity),
            isUnlocked: false,
            unlockedAt: null,
            requirements: [achievement.description],
            progress: achievement.progress ? {
              current: achievement.progress,
              required: 100
            } : undefined
          }))
        ]

        setAchievements(allAchievements)

        const stats = data.data?.stats || {}
        const totalEarnedPoints = earnedAchievements.reduce((sum: number, a: any) => sum + getRarityPoints(a.rarity), 0)
        const totalPossiblePoints = (stats.total || 0) * 50 // Average points per achievement

        setStats({
          totalAchievements: stats.total || 0,
          unlockedAchievements: stats.earned || 0,
          totalPoints: totalPossiblePoints,
          earnedPoints: totalEarnedPoints,
          completionPercentage: stats.completionRate || 0,
          rank: stats.rank || 0,
          totalUsers: stats.totalUsers || 0
        })
      } else {
        throw new Error(data.message || 'Failed to fetch achievements')
      }

    } catch (error) {
      console.error('Error fetching achievements:', error)
      toast.error('Failed to load achievements. Please try again.')

      // Set empty state to prevent crashes
      setAchievements([])
      setStats({
        totalAchievements: 0,
        unlockedAchievements: 0,
        totalPoints: 0,
        earnedPoints: 0,
        completionPercentage: 0,
        rank: 0,
        totalUsers: 0
      })
    } finally {
      setLoading(false)
    }
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-800'
      case 'rare':
        return 'border-blue-300 bg-blue-50 dark:border-blue-600 dark:bg-blue-900/20'
      case 'epic':
        return 'border-purple-300 bg-purple-50 dark:border-purple-600 dark:bg-purple-900/20'
      case 'legendary':
        return 'border-yellow-300 bg-yellow-50 dark:border-yellow-600 dark:bg-yellow-900/20'
      default:
        return 'border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-800'
    }
  }

  const getRarityGradient = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'bg-gradient-to-r from-gray-400 via-gray-500 to-gray-600'
      case 'rare':
        return 'bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600'
      case 'epic':
        return 'bg-gradient-to-r from-purple-400 via-purple-500 to-purple-600'
      case 'legendary':
        return 'bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500'
      default:
        return 'bg-gradient-to-r from-gray-400 via-gray-500 to-gray-600'
    }
  }

  const getRarityGlow = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'bg-gray-400'
      case 'rare':
        return 'bg-blue-400'
      case 'epic':
        return 'bg-purple-400'
      case 'legendary':
        return 'bg-yellow-400'
      default:
        return 'bg-gray-400'
    }
  }

  const getRarityBadgeColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
      case 'rare':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
      case 'epic':
        return 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
      case 'legendary':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 text-yellow-700 dark:text-yellow-300'
      default:
        return 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
    }
  }

  const getRarityIcon = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return <Shield className="h-4 w-4 text-gray-600" />
      case 'rare':
        return <Gem className="h-4 w-4 text-blue-600" />
      case 'epic':
        return <Star className="h-4 w-4 text-purple-600" />
      case 'legendary':
        return <Crown className="h-4 w-4 text-yellow-600" />
      default:
        return <Shield className="h-4 w-4 text-gray-600" />
    }
  }

  const filteredAchievements = achievements.filter(achievement => {
    const matchesSearch = achievement.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         achievement.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || achievement.category === selectedCategory
    const matchesRarity = selectedRarity === 'all' || achievement.rarity === selectedRarity
    const matchesStatus = selectedStatus === 'all' || 
                         (selectedStatus === 'unlocked' && achievement.isUnlocked) ||
                         (selectedStatus === 'locked' && !achievement.isUnlocked)
    
    return matchesSearch && matchesCategory && matchesRarity && matchesStatus
  })

  const unlockedAchievements = filteredAchievements.filter(a => a.isUnlocked)
  const lockedAchievements = filteredAchievements.filter(a => !a.isUnlocked)

  return (
    <div className="p-6 space-y-8 min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      {/* Header */}
      <div className="relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

        <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl shadow-lg">
                  <Trophy className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Achievements
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    Track your learning milestones and unlock rewards
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Filter className="h-4 w-4 mr-2" />
                {showFilters ? 'Hide Filters' : 'Show Filters'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Achievement Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-yellow-50/80 to-orange-50/80 dark:from-yellow-950/20 dark:to-orange-950/20 backdrop-blur-sm hover:shadow-2xl hover:shadow-yellow-500/20 transition-all duration-500 hover:-translate-y-1">
              {/* Gradient Border */}
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/30 via-orange-400/30 to-red-400/30 rounded-lg p-[1px]">
                <div className="h-full w-full bg-white/90 dark:bg-gray-900/90 rounded-lg" />
              </div>

              <CardContent className="relative z-10 pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-4xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                      {stats.unlockedAchievements}
                    </div>
                    <p className="text-sm font-medium text-muted-foreground">Unlocked</p>
                  </div>
                  <div className="p-3 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Trophy className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="mt-4 space-y-2">
                  <Progress value={stats.completionPercentage} className="h-3 bg-gray-200 dark:bg-gray-700" />
                  <p className="text-xs text-muted-foreground">
                    {stats.unlockedAchievements} of {stats.totalAchievements} achievements
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-purple-50/80 to-pink-50/80 dark:from-purple-950/20 dark:to-pink-950/20 backdrop-blur-sm hover:shadow-2xl hover:shadow-purple-500/20 transition-all duration-500 hover:-translate-y-1">
              {/* Gradient Border */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400/30 via-pink-400/30 to-rose-400/30 rounded-lg p-[1px]">
                <div className="h-full w-full bg-white/90 dark:bg-gray-900/90 rounded-lg" />
              </div>

              <CardContent className="relative z-10 pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      {stats.earnedPoints}
                    </div>
                    <p className="text-sm font-medium text-muted-foreground">Points Earned</p>
                  </div>
                  <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Star className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="mt-4">
                  <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                    <span>Progress</span>
                    <span>{stats.earnedPoints} / {stats.totalPoints}</span>
                  </div>
                  <Progress
                    value={(stats.earnedPoints / stats.totalPoints) * 100}
                    className="h-3 bg-gray-200 dark:bg-gray-700"
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-blue-50/80 to-indigo-50/80 dark:from-blue-950/20 dark:to-indigo-950/20 backdrop-blur-sm hover:shadow-2xl hover:shadow-blue-500/20 transition-all duration-500 hover:-translate-y-1">
              {/* Gradient Border */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 via-indigo-400/30 to-purple-400/30 rounded-lg p-[1px]">
                <div className="h-full w-full bg-white/90 dark:bg-gray-900/90 rounded-lg" />
              </div>

              <CardContent className="relative z-10 pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                      #{stats.rank}
                    </div>
                    <p className="text-sm font-medium text-muted-foreground">Achievement Rank</p>
                  </div>
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Award className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-xs text-muted-foreground">
                    Out of {stats.totalUsers.toLocaleString()} users
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-green-50/80 to-emerald-50/80 dark:from-green-950/20 dark:to-emerald-950/20 backdrop-blur-sm hover:shadow-2xl hover:shadow-green-500/20 transition-all duration-500 hover:-translate-y-1">
              {/* Gradient Border */}
              <div className="absolute inset-0 bg-gradient-to-r from-green-400/30 via-emerald-400/30 to-teal-400/30 rounded-lg p-[1px]">
                <div className="h-full w-full bg-white/90 dark:bg-gray-900/90 rounded-lg" />
              </div>

              <CardContent className="relative z-10 pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                      {Math.round(stats.completionPercentage)}%
                    </div>
                    <p className="text-sm font-medium text-muted-foreground">Completion</p>
                  </div>
                  <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Target className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="mt-4">
                  <Progress value={stats.completionPercentage} className="h-3 bg-gray-200 dark:bg-gray-700" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      )}

      {/* Search and Filters */}
      <Card className="border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-xl">
        <CardContent className="pt-6">
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search achievements..."
                className="pl-12 h-12 border-0 bg-gray-50 dark:bg-gray-800/50 backdrop-blur-sm shadow-sm focus:shadow-lg transition-all duration-300"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Advanced Filters */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t"
                >
                  <div>
                    <label className="text-sm font-medium mb-2 block">Category</label>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        {categories.map(category => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Rarity</label>
                    <Select value={selectedRarity} onValueChange={setSelectedRarity}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Rarities</SelectItem>
                        <SelectItem value="common">Common</SelectItem>
                        <SelectItem value="rare">Rare</SelectItem>
                        <SelectItem value="epic">Epic</SelectItem>
                        <SelectItem value="legendary">Legendary</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Status</label>
                    <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Achievements</SelectItem>
                        <SelectItem value="unlocked">Unlocked</SelectItem>
                        <SelectItem value="locked">Locked</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {filteredAchievements.length} of {achievements.length} achievements
        </p>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="pt-6">
                  <div className="h-16 bg-muted rounded mb-4"></div>
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="pt-6">
                  <div className="h-32 bg-muted rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Achievements */}
      {!loading && (
        <Tabs defaultValue="all" className="space-y-6">
        <TabsList>
          <TabsTrigger value="all">All ({filteredAchievements.length})</TabsTrigger>
          <TabsTrigger value="unlocked">Unlocked ({unlockedAchievements.length})</TabsTrigger>
          <TabsTrigger value="locked">Locked ({lockedAchievements.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredAchievements.map((achievement, index) => (
              <motion.div
                key={achievement.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.1 * index }}
                className={`group relative overflow-hidden rounded-2xl border-0 transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 ${
                  achievement.isUnlocked
                    ? 'bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/70 backdrop-blur-sm shadow-lg'
                    : 'bg-gradient-to-br from-gray-100/80 to-gray-200/60 dark:from-gray-800/60 dark:to-gray-900/40 backdrop-blur-sm opacity-75'
                }`}
              >
                {/* Gradient Border */}
                <div className={`absolute inset-0 rounded-2xl p-[2px] ${
                  achievement.isUnlocked
                    ? getRarityGradient(achievement.rarity)
                    : 'bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-700'
                }`}>
                  <div className="h-full w-full bg-white dark:bg-gray-900 rounded-2xl" />
                </div>

                {/* Lock Icon for Locked Achievements */}
                {!achievement.isUnlocked && (
                  <div className="absolute top-4 right-4 z-20">
                    <div className="p-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                      <Lock className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                )}

                {/* Rarity Glow Effect */}
                {achievement.isUnlocked && (
                  <div className={`absolute inset-0 rounded-2xl opacity-20 blur-xl ${getRarityGlow(achievement.rarity)}`} />
                )}

                <div className="relative z-10 p-6">
                  <div className="flex items-start gap-4">
                    <div className={`relative ${!achievement.isUnlocked ? 'grayscale' : ''}`}>
                      <div className={`text-5xl ${achievement.isUnlocked ? 'animate-pulse' : ''}`}>
                        {achievement.icon}
                      </div>
                      {achievement.isUnlocked && (
                        <div className="absolute -top-1 -right-1">
                          <div className="w-4 h-4 bg-green-500 rounded-full animate-ping" />
                          <div className="absolute inset-0 w-4 h-4 bg-green-500 rounded-full" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-bold text-lg">{achievement.title}</h3>
                        {getRarityIcon(achievement.rarity)}
                      </div>
                      <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
                        {achievement.description}
                      </p>

                      <div className="flex items-center gap-2 mb-4">
                        <Badge
                          variant="outline"
                          className={`text-xs font-medium border-0 ${getRarityBadgeColor(achievement.rarity)}`}
                        >
                          {achievement.category}
                        </Badge>
                        <Badge
                          variant="outline"
                          className={`text-xs font-medium border-0 ${getRarityBadgeColor(achievement.rarity)}`}
                        >
                          {achievement.rarity}
                        </Badge>
                        <Badge
                          variant="outline"
                          className="text-xs font-medium border-0 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300"
                        >
                          {achievement.points} pts
                        </Badge>
                      </div>

                      {achievement.isUnlocked ? (
                        <div className="flex items-center gap-2 p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          <span className="text-sm font-medium text-green-700 dark:text-green-300">
                            Unlocked {new Date(achievement.unlockedAt!).toLocaleDateString()}
                          </span>
                        </div>
                      ) : achievement.progress ? (
                        <div className="space-y-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg">
                          <div className="flex justify-between text-sm font-medium">
                            <span>Progress</span>
                            <span>{achievement.progress.current}/{achievement.progress.required}</span>
                          </div>
                          <Progress
                            value={(achievement.progress.current / achievement.progress.required) * 100}
                            className="h-3 bg-gray-200 dark:bg-gray-700"
                          />
                        </div>
                      ) : (
                        <div className="p-3 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/50 rounded-lg">
                          <div className="text-sm text-muted-foreground">
                            <span className="font-medium">Requirements:</span> {achievement.requirements.join(', ')}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="unlocked">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {unlockedAchievements.map((achievement, index) => (
              <motion.div
                key={achievement.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.1 * index }}
                className={`p-6 rounded-lg border-2 transition-all hover:shadow-lg ${getRarityColor(achievement.rarity)}`}
              >
                <div className="flex items-start gap-4">
                  <div className="text-4xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold">{achievement.title}</h3>
                      {getRarityIcon(achievement.rarity)}
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      {achievement.description}
                    </p>
                    
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="outline" className="text-xs">
                        {achievement.category}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {achievement.rarity}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {achievement.points} pts
                      </Badge>
                    </div>

                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      <span>Unlocked {new Date(achievement.unlockedAt!).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="locked">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {lockedAchievements.map((achievement, index) => (
              <motion.div
                key={achievement.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.1 * index }}
                className="relative p-6 rounded-lg border-2 border-gray-200 bg-gray-50/50 dark:border-gray-700 dark:bg-gray-800/50 opacity-75 transition-all hover:shadow-lg"
              >
                <div className="absolute top-2 right-2">
                  <Lock className="h-4 w-4 text-muted-foreground" />
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="text-4xl grayscale">{achievement.icon}</div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold">{achievement.title}</h3>
                      {getRarityIcon(achievement.rarity)}
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      {achievement.description}
                    </p>
                    
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="outline" className="text-xs">
                        {achievement.category}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {achievement.rarity}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {achievement.points} pts
                      </Badge>
                    </div>

                    {achievement.progress ? (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{achievement.progress.current}/{achievement.progress.required}</span>
                        </div>
                        <Progress 
                          value={(achievement.progress.current / achievement.progress.required) * 100} 
                          className="h-2" 
                        />
                      </div>
                    ) : (
                      <div className="text-sm text-muted-foreground">
                        Requirements: {achievement.requirements.join(', ')}
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
      )}

      {!loading && filteredAchievements.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Award className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No achievements found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search criteria or filters.
              </p>
              <Button onClick={() => {
                setSearchQuery("")
                setSelectedCategory("all")
                setSelectedRarity("all")
                setSelectedStatus("all")
              }}>
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
