"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  Clock, 
  Users, 
  Settings, 
  Calendar,
  Eye,
  BookOpen,
  Target
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"

interface Quiz {
  id: string
  title: string
  description?: string
  difficulty: string
  timeLimit?: number
  questionCount: number
  category?: string
  tags: string[]
  isPublished: boolean
}

interface CreateLiveQuizDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function CreateLiveQuizDialog({ open, onOpenChange, onSuccess }: CreateLiveQuizDialogProps) {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [quizzes, setQuizzes] = useState<Quiz[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedQuiz, setSelectedQuiz] = useState<Quiz | null>(null)

  // Form data
  const [formData, setFormData] = useState({
    quizId: "",
    title: "",
    description: "",
    maxParticipants: "",
    questionTimeLimit: "30",
    autoAdvance: true,
    showLeaderboard: true,
    allowLateJoin: false,
    scheduledStart: ""
  })

  useEffect(() => {
    if (open) {
      fetchQuizzes()
      resetForm()
    }
  }, [open])

  const resetForm = () => {
    setStep(1)
    setSelectedQuiz(null)
    setFormData({
      quizId: "",
      title: "",
      description: "",
      maxParticipants: "",
      questionTimeLimit: "30",
      autoAdvance: true,
      showLeaderboard: true,
      allowLateJoin: false,
      scheduledStart: ""
    })
  }

  const fetchQuizzes = async () => {
    try {
      const response = await fetch('/api/admin/quizzes?limit=50&published=true')
      const data = await response.json()
      
      if (data.success) {
        setQuizzes(data.data.quizzes)
      } else {
        throw new Error(data.message || 'Failed to fetch quizzes')
      }
    } catch (error) {
      console.error('Error fetching quizzes:', error)
      toast.error('Failed to fetch quizzes')
    }
  }

  const handleQuizSelect = (quiz: Quiz) => {
    setSelectedQuiz(quiz)
    setFormData(prev => ({
      ...prev,
      quizId: quiz.id,
      title: `Live: ${quiz.title}`,
      description: quiz.description || ""
    }))
    setStep(2)
  }

  const handleSubmit = async () => {
    if (!selectedQuiz) return

    setLoading(true)
    try {
      const payload = {
        quizId: formData.quizId,
        title: formData.title,
        description: formData.description || undefined,
        maxParticipants: formData.maxParticipants ? parseInt(formData.maxParticipants) : undefined,
        questionTimeLimit: parseInt(formData.questionTimeLimit),
        autoAdvance: formData.autoAdvance,
        showLeaderboard: formData.showLeaderboard,
        allowLateJoin: formData.allowLateJoin,
        scheduledStart: formData.scheduledStart || undefined
      }

      const response = await fetch('/api/admin/live-quiz/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Live quiz session created successfully!')
        onSuccess()
      } else {
        throw new Error(data.message || 'Failed to create live quiz session')
      }
    } catch (error) {
      console.error('Error creating live quiz session:', error)
      toast.error('Failed to create live quiz session')
    } finally {
      setLoading(false)
    }
  }

  const filteredQuizzes = quizzes.filter(quiz =>
    quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    quiz.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    quiz.category?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Live Quiz Session</DialogTitle>
          <DialogDescription>
            {step === 1 ? 'Select a quiz to create a live session' : 'Configure your live quiz session'}
          </DialogDescription>
        </DialogHeader>

        <AnimatePresence mode="wait">
          {step === 1 ? (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-4"
            >
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search quizzes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Quiz Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {filteredQuizzes.map((quiz) => (
                  <motion.div
                    key={quiz.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card 
                      className="cursor-pointer hover:border-primary transition-colors"
                      onClick={() => handleQuizSelect(quiz)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <CardTitle className="text-base">{quiz.title}</CardTitle>
                          <Badge variant="outline">{quiz.difficulty}</Badge>
                        </div>
                        {quiz.description && (
                          <CardDescription className="text-sm line-clamp-2">
                            {quiz.description}
                          </CardDescription>
                        )}
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            <span>{quiz.questionCount} questions</span>
                          </div>
                          {quiz.timeLimit && (
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              <span>{quiz.timeLimit}m</span>
                            </div>
                          )}
                        </div>
                        {quiz.category && (
                          <Badge variant="secondary" className="mt-2">
                            {quiz.category}
                          </Badge>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {filteredQuizzes.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No published quizzes found</p>
                  <p className="text-sm">Create and publish a quiz first</p>
                </div>
              )}
            </motion.div>
          ) : (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              {/* Selected Quiz Info */}
              {selectedQuiz && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Selected Quiz</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{selectedQuiz.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          {selectedQuiz.questionCount} questions • {selectedQuiz.difficulty}
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setStep(1)}
                      >
                        Change
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Session Configuration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Settings */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Basic Settings</h3>
                  
                  <div className="space-y-2">
                    <Label htmlFor="title">Session Title</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="Enter session title"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description (Optional)</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Enter session description"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="scheduledStart">Scheduled Start (Optional)</Label>
                    <Input
                      id="scheduledStart"
                      type="datetime-local"
                      value={formData.scheduledStart}
                      onChange={(e) => setFormData(prev => ({ ...prev, scheduledStart: e.target.value }))}
                    />
                  </div>
                </div>

                {/* Advanced Settings */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Advanced Settings</h3>
                  
                  <div className="space-y-2">
                    <Label htmlFor="maxParticipants">Max Participants (Optional)</Label>
                    <Input
                      id="maxParticipants"
                      type="number"
                      min="1"
                      value={formData.maxParticipants}
                      onChange={(e) => setFormData(prev => ({ ...prev, maxParticipants: e.target.value }))}
                      placeholder="No limit"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="questionTimeLimit">Question Time Limit (seconds)</Label>
                    <Input
                      id="questionTimeLimit"
                      type="number"
                      min="10"
                      max="300"
                      value={formData.questionTimeLimit}
                      onChange={(e) => setFormData(prev => ({ ...prev, questionTimeLimit: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="autoAdvance">Auto Advance Questions</Label>
                      <Switch
                        id="autoAdvance"
                        checked={formData.autoAdvance}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, autoAdvance: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="showLeaderboard">Show Live Leaderboard</Label>
                      <Switch
                        id="showLeaderboard"
                        checked={formData.showLeaderboard}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showLeaderboard: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="allowLateJoin">Allow Late Join</Label>
                      <Switch
                        id="allowLateJoin"
                        checked={formData.allowLateJoin}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, allowLateJoin: checked }))}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <Separator />
              <div className="flex justify-between pt-4">
                <Button
                  variant="outline"
                  onClick={() => setStep(1)}
                >
                  Back
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={loading || !formData.title.trim()}
                >
                  {loading ? 'Creating...' : 'Create Session'}
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  )
}
