"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { getSocketClient } from "@/lib/socket-client"
import { toast } from "@/lib/toast-utils"

interface LiveQuizSyncState {
  sessionId: string
  currentQuestion: number
  timeRemaining: number | null
  isConnected: boolean
  participants: number
  leaderboard: Array<{
    userId: string
    userName: string
    score: number
    rank: number
  }>
  sessionStatus: 'WAITING' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED'
}

interface LiveQuizSyncOptions {
  sessionId: string
  onQuestionChange?: (questionData: any) => void
  onSessionStatusChange?: (status: string) => void
  onAnswerFeedback?: (feedback: any) => void
  onParticipantUpdate?: (participants: number) => void
  onLeaderboardUpdate?: (leaderboard: any[]) => void
  autoReconnect?: boolean
}

export function useLiveQuizSync({
  sessionId,
  onQuestionChange,
  onSessionStatusChange,
  onAnswerFeedback,
  onParticipantUpdate,
  onLeaderboardUpdate,
  autoReconnect = true
}: LiveQuizSyncOptions) {
  const [state, setState] = useState<LiveQuizSyncState>({
    sessionId,
    currentQuestion: 0,
    timeRemaining: null,
    isConnected: false,
    participants: 0,
    leaderboard: [],
    sessionStatus: 'WAITING'
  })

  const socketClient = useRef(getSocketClient())
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = useRef(1000)

  // Question timer management
  const questionTimer = useRef<NodeJS.Timeout | null>(null)
  const questionStartTime = useRef<number | null>(null)

  const startQuestionTimer = useCallback((timeLimit: number) => {
    if (questionTimer.current) {
      clearInterval(questionTimer.current)
    }

    questionStartTime.current = Date.now()
    setState(prev => ({ ...prev, timeRemaining: timeLimit }))

    questionTimer.current = setInterval(() => {
      setState(prev => {
        if (prev.timeRemaining === null || prev.timeRemaining <= 1) {
          if (questionTimer.current) {
            clearInterval(questionTimer.current)
            questionTimer.current = null
          }
          return { ...prev, timeRemaining: null }
        }
        return { ...prev, timeRemaining: prev.timeRemaining - 1 }
      })
    }, 1000)
  }, [])

  const stopQuestionTimer = useCallback(() => {
    if (questionTimer.current) {
      clearInterval(questionTimer.current)
      questionTimer.current = null
    }
    setState(prev => ({ ...prev, timeRemaining: null }))
  }, [])

  const setupSocketListeners = useCallback(() => {
    const socket = socketClient.current

    // Connection events
    socket.on('connect', () => {
      setState(prev => ({ ...prev, isConnected: true }))
      reconnectAttempts.current = 0
      reconnectDelay.current = 1000
      
      // Rejoin the session room
      socket.joinLiveQuizSession(sessionId)
      toast.success('Connected to live quiz')
    })

    socket.on('disconnect', () => {
      setState(prev => ({ ...prev, isConnected: false }))
      stopQuestionTimer()
      
      if (autoReconnect && reconnectAttempts.current < maxReconnectAttempts) {
        setTimeout(() => {
          reconnectAttempts.current++
          reconnectDelay.current = Math.min(reconnectDelay.current * 2, 10000)
          socket.connect()
        }, reconnectDelay.current)
      }
    })

    // Session events
    socket.on('live-quiz:session-started', (data: any) => {
      if (data.sessionId === sessionId) {
        setState(prev => ({ ...prev, sessionStatus: 'ACTIVE' }))
        onSessionStatusChange?.('ACTIVE')
        
        if (data.currentQuestionData && data.session.questionTimeLimit) {
          startQuestionTimer(data.session.questionTimeLimit)
        }
      }
    })

    socket.on('live-quiz:session-paused', (data: any) => {
      if (data.sessionId === sessionId) {
        setState(prev => ({ ...prev, sessionStatus: 'PAUSED' }))
        onSessionStatusChange?.('PAUSED')
        stopQuestionTimer()
      }
    })

    socket.on('live-quiz:session-resumed', (data: any) => {
      if (data.sessionId === sessionId) {
        setState(prev => ({ ...prev, sessionStatus: 'ACTIVE' }))
        onSessionStatusChange?.('ACTIVE')
        
        // Resume timer if there's remaining time
        if (data.session.questionTimeLimit) {
          startQuestionTimer(data.session.questionTimeLimit)
        }
      }
    })

    socket.on('live-quiz:session-completed', (data: any) => {
      if (data.sessionId === sessionId) {
        setState(prev => ({ 
          ...prev, 
          sessionStatus: 'COMPLETED',
          leaderboard: data.finalRankings || []
        }))
        onSessionStatusChange?.('COMPLETED')
        onLeaderboardUpdate?.(data.finalRankings || [])
        stopQuestionTimer()
      }
    })

    // Question events
    socket.on('live-quiz:question-changed', (data: any) => {
      if (data.sessionId === sessionId) {
        setState(prev => ({ 
          ...prev, 
          currentQuestion: data.session.currentQuestion 
        }))
        
        onQuestionChange?.(data.questionData)
        
        // Start timer for new question
        if (data.session.questionTimeLimit) {
          startQuestionTimer(data.session.questionTimeLimit)
        }
      }
    })

    socket.on('live-quiz:current-question', (data: any) => {
      if (data.sessionId === sessionId) {
        onQuestionChange?.(data.questionData)
        
        if (data.timeLimit) {
          startQuestionTimer(data.timeLimit)
        }
      }
    })

    // Answer events
    socket.on('live-quiz:answer-feedback', (data: any) => {
      if (data.sessionId === sessionId) {
        onAnswerFeedback?.(data)
      }
    })

    socket.on('live-quiz:answer-submitted', (data: any) => {
      if (data.sessionId === sessionId) {
        // Update participant count or other relevant state
        // This could trigger a leaderboard refresh
      }
    })

    // Participant events
    socket.on('live-quiz:participant-joined', (data: any) => {
      if (data.sessionId === sessionId) {
        setState(prev => ({ 
          ...prev, 
          participants: prev.participants + 1 
        }))
        onParticipantUpdate?.(state.participants + 1)
      }
    })

    socket.on('live-quiz:participant-left', (data: any) => {
      if (data.sessionId === sessionId) {
        setState(prev => ({ 
          ...prev, 
          participants: Math.max(0, prev.participants - 1) 
        }))
        onParticipantUpdate?.(Math.max(0, state.participants - 1))
      }
    })

    socket.on('live-quiz:participant-progress', (data: any) => {
      if (data.sessionId === sessionId) {
        // Could update leaderboard or other participant-related state
      }
    })

    // Leaderboard events
    socket.on('live-quiz:leaderboard-updated', (data: any) => {
      if (data.sessionId === sessionId) {
        setState(prev => ({ ...prev, leaderboard: data.leaderboard }))
        onLeaderboardUpdate?.(data.leaderboard)
      }
    })

  }, [sessionId, onQuestionChange, onSessionStatusChange, onAnswerFeedback, onParticipantUpdate, onLeaderboardUpdate, autoReconnect, startQuestionTimer, stopQuestionTimer])

  const cleanupSocketListeners = useCallback(() => {
    const socket = socketClient.current
    
    socket.off('connect')
    socket.off('disconnect')
    socket.off('live-quiz:session-started')
    socket.off('live-quiz:session-paused')
    socket.off('live-quiz:session-resumed')
    socket.off('live-quiz:session-completed')
    socket.off('live-quiz:question-changed')
    socket.off('live-quiz:current-question')
    socket.off('live-quiz:answer-feedback')
    socket.off('live-quiz:answer-submitted')
    socket.off('live-quiz:participant-joined')
    socket.off('live-quiz:participant-left')
    socket.off('live-quiz:participant-progress')
    socket.off('live-quiz:leaderboard-updated')
  }, [])

  // Initialize socket connection and listeners
  useEffect(() => {
    setupSocketListeners()
    
    // Join the session room
    socketClient.current.joinLiveQuizSession(sessionId)
    
    return () => {
      cleanupSocketListeners()
      socketClient.current.leaveLiveQuizSession(sessionId)
      stopQuestionTimer()
    }
  }, [sessionId, setupSocketListeners, cleanupSocketListeners, stopQuestionTimer])

  // Sync progress with server
  const syncProgress = useCallback((currentQuestion: number, score: number, timeSpent: number) => {
    socketClient.current.syncLiveQuizProgress({
      sessionId,
      currentQuestion,
      score,
      timeSpent
    })
  }, [sessionId])

  // Submit answer with real-time notification
  const submitAnswer = useCallback((questionId: string, answer: any, timeSpent: number) => {
    socketClient.current.submitLiveQuizAnswer({
      sessionId,
      questionId,
      answer,
      timeSpent
    })
  }, [sessionId])

  // Request current question (useful for reconnection)
  const requestCurrentQuestion = useCallback(() => {
    socketClient.current.requestCurrentQuestion(sessionId)
  }, [sessionId])

  // Manual reconnection
  const reconnect = useCallback(() => {
    if (!state.isConnected) {
      socketClient.current.connect()
    }
  }, [state.isConnected])

  return {
    // State
    ...state,
    
    // Actions
    syncProgress,
    submitAnswer,
    requestCurrentQuestion,
    reconnect,
    startQuestionTimer,
    stopQuestionTimer,
    
    // Computed values
    isTimerActive: questionTimer.current !== null,
    timeElapsed: questionStartTime.current ? Math.floor((Date.now() - questionStartTime.current) / 1000) : 0
  }
}
