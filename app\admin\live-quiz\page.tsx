"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  Plus, 
  Search, 
  Filter,
  Play,
  Pause,
  Square,
  Users,
  Clock,
  BarChart3,
  Settings,
  Eye,
  Edit,
  Trash2,
  RefreshCw
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { AdminLayout } from "@/components/admin/admin-layout"
import { CreateLiveQuizDialog } from "@/components/admin/live-quiz/create-live-quiz-dialog"
import { LiveQuizSessionCard } from "@/components/admin/live-quiz/live-quiz-session-card"
import { LiveQuizMonitor } from "@/components/admin/live-quiz/live-quiz-monitor"

interface LiveQuizSession {
  id: string
  title: string
  description?: string
  status: 'WAITING' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED'
  maxParticipants?: number
  currentQuestion: number
  questionTimeLimit?: number
  autoAdvance: boolean
  showLeaderboard: boolean
  allowLateJoin: boolean
  startTime?: string
  endTime?: string
  scheduledStart?: string
  createdAt: string
  quiz: {
    id: string
    title: string
    description?: string
    difficulty: string
    timeLimit?: number
    questionCount: number
  }
  creator: {
    id: string
    name: string
  }
  participantCount: number
}

export default function LiveQuizManagement() {
  const [sessions, setSessions] = useState<LiveQuizSession[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [selectedSession, setSelectedSession] = useState<LiveQuizSession | null>(null)
  const [showMonitor, setShowMonitor] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 12

  useEffect(() => {
    fetchSessions()
    // Set up auto-refresh for active sessions
    const interval = setInterval(() => {
      if (!refreshing) {
        fetchSessions(true) // Silent refresh
      }
    }, 30000) // Refresh every 30 seconds

    return () => clearInterval(interval)
  }, [currentPage, statusFilter, searchTerm])

  const fetchSessions = async (silent = false) => {
    if (!silent) setLoading(true)
    setRefreshing(true)

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        ...(statusFilter !== 'all' && { status: statusFilter }),
        ...(searchTerm && { search: searchTerm })
      })

      const response = await fetch(`/api/admin/live-quiz/sessions?${params}`)
      const data = await response.json()

      if (data.success) {
        setSessions(data.data.sessions)
        setTotalPages(data.data.pagination.totalPages)
      } else {
        throw new Error(data.message || 'Failed to fetch sessions')
      }
    } catch (error) {
      console.error('Error fetching live quiz sessions:', error)
      if (!silent) {
        toast.error('Failed to fetch live quiz sessions')
      }
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleSessionAction = async (sessionId: string, action: 'start' | 'stop' | 'pause' | 'resume') => {
    try {
      let endpoint = ''
      let body = {}

      switch (action) {
        case 'start':
          endpoint = `/api/admin/live-quiz/sessions/${sessionId}/start`
          break
        case 'stop':
          endpoint = `/api/admin/live-quiz/sessions/${sessionId}/stop`
          break
        case 'pause':
        case 'resume':
          endpoint = `/api/admin/live-quiz/sessions/${sessionId}/pause`
          body = { action }
          break
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Session ${action}ed successfully`)
        fetchSessions()
      } else {
        throw new Error(data.message || `Failed to ${action} session`)
      }
    } catch (error) {
      console.error(`Error ${action}ing session:`, error)
      toast.error(`Failed to ${action} session`)
    }
  }

  const handleDeleteSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/admin/live-quiz/sessions/${sessionId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Session deleted successfully')
        fetchSessions()
      } else {
        throw new Error(data.message || 'Failed to delete session')
      }
    } catch (error) {
      console.error('Error deleting session:', error)
      toast.error('Failed to delete session')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'WAITING': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'ACTIVE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'PAUSED': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'COMPLETED': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'CANCELLED': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const filteredSessions = sessions.filter(session => {
    const matchesSearch = !searchTerm || 
      session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.quiz.title.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || session.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Live Quiz Management</h1>
            <p className="text-muted-foreground">Create and manage live quiz sessions</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchSessions()}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Live Quiz
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search sessions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="WAITING">Waiting</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="PAUSED">Paused</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Sessions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {loading ? (
              // Loading skeletons
              Array.from({ length: 6 }).map((_, i) => (
                <motion.div
                  key={`skeleton-${i}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.1 }}
                >
                  <Card className="h-64 animate-pulse">
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                        <div className="h-3 bg-muted rounded w-full"></div>
                        <div className="h-3 bg-muted rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            ) : filteredSessions.length > 0 ? (
              filteredSessions.map((session, index) => (
                <motion.div
                  key={session.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <LiveQuizSessionCard
                    session={session}
                    onAction={handleSessionAction}
                    onDelete={handleDeleteSession}
                    onMonitor={(session) => {
                      setSelectedSession(session)
                      setShowMonitor(true)
                    }}
                  />
                </motion.div>
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="col-span-full"
              >
                <Card>
                  <CardContent className="pt-6 text-center">
                    <div className="text-muted-foreground">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No live quiz sessions found</p>
                      <p className="text-sm">Create your first live quiz session to get started</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="flex items-center px-4 text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}
      </div>

      {/* Create Dialog */}
      <CreateLiveQuizDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={() => {
          setShowCreateDialog(false)
          fetchSessions()
        }}
      />

      {/* Monitor Dialog */}
      {selectedSession && (
        <LiveQuizMonitor
          session={selectedSession}
          open={showMonitor}
          onOpenChange={setShowMonitor}
          onSessionUpdate={fetchSessions}
        />
      )}
    </AdminLayout>
  )
}
