"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  FileText,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Calendar,
  Users,
  Clock,
  Download,
  Upload,
  Copy,
  Archive,
  CheckSquare,
  Square,
  RefreshCw,
  SortAsc,
  SortDesc,
  Settings,
  BarChart3,
  Globe,
  Lock,
  AlertTriangle
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "@/lib/toast-utils"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { DeleteConfirmationDialog, BulkActionDialog } from "@/components/ui/confirmation-dialog"

interface Quiz {
  id: string
  title: string
  description?: string
  type: 'QUIZ' | 'TEST_SERIES' | 'DAILY_PRACTICE'
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  tags: string[]
  questionCount: number
  attemptCount: number
  averageScore: number
  isPublished: boolean
  createdAt: string
  updatedAt: string
  creator: {
    name: string
    email: string
  }
  timeLimit?: number
  startTime?: string
  endTime?: string
}

export default function QuizManagement() {
  const [quizzes, setQuizzes] = useState<Quiz[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedQuizzes, setSelectedQuizzes] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [difficultyFilter, setDifficultyFilter] = useState("all")
  const [sortBy, setSortBy] = useState("createdAt")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [showFilters, setShowFilters] = useState(false)

  // Fetch quizzes from API
  const fetchQuizzes = async () => {
    setLoading(true)
    setError(null)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        search: searchQuery,
        type: typeFilter !== 'all' ? typeFilter : '',
        status: statusFilter !== 'all' ? statusFilter : '',
        difficulty: difficultyFilter !== 'all' ? difficultyFilter : '',
        sortBy,
        sortOrder
      })

      const response = await fetch(`/api/admin/quizzes?${params}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        if (response.status === 401) {
          toast.error('You are not authorized to view quizzes')
          return
        }

        if (response.status === 404) {
          // Handle 404 gracefully - this might mean no quizzes exist
          setQuizzes([])
          setTotalPages(1)
          return
        }

        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch quizzes`)
      }

      const data = await response.json()

      // Handle successful response
      if (data.quizzes) {
        setQuizzes(data.quizzes)
        setTotalPages(data.pagination?.pages || 1)

        // Only show success message if there are quizzes and it's a manual refresh
        if (data.quizzes.length > 0 && currentPage === 1) {
          console.log(`Loaded ${data.quizzes.length} quiz(es)`)
        }
      } else {
        // Handle unexpected response format
        setQuizzes([])
        setTotalPages(1)
        console.warn('Unexpected API response format:', data)
      }

    } catch (error) {
      console.error('Error fetching quizzes:', error)

      // Set empty state on error
      setQuizzes([])
      setTotalPages(1)

      // Set error state for UI display
      if (error instanceof Error) {
        setError(error.message)
      } else {
        setError('Failed to load quizzes. Please try again.')
      }

      // Don't show toast for every error - let the UI handle it
      console.warn('Quiz fetch error:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchQuizzes()
  }, [currentPage, searchQuery, typeFilter, statusFilter, difficultyFilter, sortBy, sortOrder])

  const getStatusColor = (isPublished: boolean) => {
    return isPublished
      ? 'bg-green-500 hover:bg-green-600'
      : 'bg-yellow-500 hover:bg-yellow-600'
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800'
      case 'HARD':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800'
    }
  }

  // Bulk operations
  const handleSelectAll = () => {
    if (selectedQuizzes.length === quizzes.length) {
      setSelectedQuizzes([])
    } else {
      setSelectedQuizzes(quizzes.map(q => q.id))
    }
  }

  const handleSelectQuiz = (quizId: string) => {
    setSelectedQuizzes(prev =>
      prev.includes(quizId)
        ? prev.filter(id => id !== quizId)
        : [...prev, quizId]
    )
  }

  const handleBulkPublish = async () => {
    if (selectedQuizzes.length === 0) return

    try {
      await Promise.all(
        selectedQuizzes.map(id =>
          fetch(`/api/admin/quizzes/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ isPublished: true })
          })
        )
      )
      toast.success(`Published ${selectedQuizzes.length} quiz(es)`)
      setSelectedQuizzes([])
      fetchQuizzes()
    } catch (error) {
      toast.error('Failed to publish quizzes')
    }
  }

  const handleBulkUnpublish = async () => {
    if (selectedQuizzes.length === 0) return

    try {
      await Promise.all(
        selectedQuizzes.map(id =>
          fetch(`/api/admin/quizzes/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ isPublished: false })
          })
        )
      )
      toast.success(`Unpublished ${selectedQuizzes.length} quiz(es)`)
      setSelectedQuizzes([])
      fetchQuizzes()
    } catch (error) {
      toast.error('Failed to unpublish quizzes')
    }
  }

  const handleBulkDelete = async () => {
    if (selectedQuizzes.length === 0) return

    try {
      await Promise.all(
        selectedQuizzes.map(id =>
          fetch(`/api/admin/quizzes/${id}`, { method: 'DELETE' })
        )
      )
      toast.success(`Deleted ${selectedQuizzes.length} quiz(es)`)
      setSelectedQuizzes([])
      fetchQuizzes()
    } catch (error) {
      toast.error('Failed to delete quizzes')
    }
  }

  const handleDeleteQuiz = async (quizId: string) => {
    try {
      const response = await fetch(`/api/admin/quizzes/${quizId}`, { method: 'DELETE' })
      if (!response.ok) throw new Error('Failed to delete quiz')

      toast.success('Quiz deleted successfully')
      fetchQuizzes()
    } catch (error) {
      toast.error('Failed to delete quiz')
    }
  }

  const handleDuplicateQuiz = async (quizId: string) => {
    try {
      const response = await fetch(`/api/admin/quizzes/${quizId}/duplicate`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to duplicate quiz')
      }

      const data = await response.json()

      if (data.success) {
        toast.success('Quiz duplicated successfully')
        fetchQuizzes() // Refresh the list to show the new quiz
      } else {
        throw new Error(data.error || 'Failed to duplicate quiz')
      }
    } catch (error) {
      console.error('Error duplicating quiz:', error)
      toast.error('Failed to duplicate quiz')
    }
  }

  const handleTogglePublish = async (quizId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/quizzes/${quizId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isPublished: !currentStatus })
      })

      if (!response.ok) throw new Error('Failed to update quiz')

      toast.success(currentStatus ? 'Quiz unpublished' : 'Quiz published')
      fetchQuizzes()
    } catch (error) {
      toast.error('Failed to update quiz status')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      <motion.div className="p-6 space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="relative"
        >
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

          <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                    <FileText className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Quiz Management
                    </h1>
                    <p className="text-muted-foreground text-lg">
                      Create, edit, and manage all your quizzes and assessments
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                  className="glass hover:bg-white/20 dark:hover:bg-gray-800/20 border-white/20 dark:border-gray-700/20"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  {showFilters ? 'Hide Filters' : 'Show Filters'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    fetchQuizzes()
                    if (!loading) {
                      toast.info('Refreshing quiz list...')
                    }
                  }}
                  disabled={loading}
                  className="glass hover:bg-white/20 dark:hover:bg-gray-800/20 border-white/20 dark:border-gray-700/20"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                <Button
                  asChild
                  className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Link href="/admin/quizzes/create">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Quiz
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl">
            <CardContent className="pt-6">
              <div className="space-y-6">
                {/* Search Bar */}
                <div className="flex items-center gap-4">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Search quizzes by title or description..."
                      className="pl-12 h-12 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 focus:bg-white/60 dark:focus:bg-gray-800/60 transition-all duration-300"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <div className="flex items-center gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                      className="glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 hover:bg-white/60 dark:hover:bg-gray-800/60 h-12 px-4"
                    >
                      {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                    </Button>
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-48 h-12 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="createdAt">Created Date</SelectItem>
                        <SelectItem value="title">Title</SelectItem>
                        <SelectItem value="attemptCount">Attempts</SelectItem>
                        <SelectItem value="averageScore">Average Score</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Advanced Filters */}
                <AnimatePresence>
                  {showFilters && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="grid grid-cols-1 md:grid-cols-4 gap-6 pt-6 border-t border-white/20 dark:border-gray-700/20"
                    >
                      <div className="space-y-2">
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">Type</Label>
                        <Select value={typeFilter} onValueChange={setTypeFilter}>
                          <SelectTrigger className="glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Types</SelectItem>
                            <SelectItem value="QUIZ">Quiz</SelectItem>
                            <SelectItem value="TEST_SERIES">Test Series</SelectItem>
                            <SelectItem value="DAILY_PRACTICE">Daily Practice</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">Status</Label>
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                          <SelectTrigger className="glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="published">Published</SelectItem>
                            <SelectItem value="draft">Draft</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">Difficulty</Label>
                        <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
                          <SelectTrigger className="glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Levels</SelectItem>
                            <SelectItem value="EASY">Easy</SelectItem>
                            <SelectItem value="MEDIUM">Medium</SelectItem>
                            <SelectItem value="HARD">Hard</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-end">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setSearchQuery("")
                            setTypeFilter("all")
                            setStatusFilter("all")
                            setDifficultyFilter("all")
                            setSortBy("createdAt")
                            setSortOrder("desc")
                          }}
                          className="w-full glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30 hover:bg-white/60 dark:hover:bg-gray-800/60 transition-all duration-300"
                        >
                          Clear Filters
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Bulk Operations Toolbar */}
        {selectedQuizzes.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="glass bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-blue-500/20 dark:border-blue-400/20 rounded-2xl p-6 shadow-xl"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <CheckSquare className="h-5 w-5 text-white" />
                  </div>
                  <span className="text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    {selectedQuizzes.length} quiz{selectedQuizzes.length !== 1 ? 'es' : ''} selected
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <Button
                    size="sm"
                    onClick={handleBulkPublish}
                    className="glass bg-green-500/20 hover:bg-green-500/30 text-green-700 dark:text-green-300 border-green-500/30"
                  >
                    <Globe className="h-4 w-4 mr-2" />
                    Publish
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleBulkUnpublish}
                    className="glass bg-orange-500/20 hover:bg-orange-500/30 text-orange-700 dark:text-orange-300 border-orange-500/30"
                  >
                    <Lock className="h-4 w-4 mr-2" />
                    Unpublish
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="glass bg-blue-500/20 hover:bg-blue-500/30 text-blue-700 dark:text-blue-300 border-blue-500/30"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                  <BulkActionDialog
                    trigger={
                      <Button
                        size="sm"
                        variant="destructive"
                        disabled={selectedQuizzes.length === 0}
                        className="glass bg-red-500/20 hover:bg-red-500/30 text-red-700 dark:text-red-300 border-red-500/30"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    }
                    action="Delete"
                    itemCount={selectedQuizzes.length}
                    itemType="quizzes"
                    onConfirm={handleBulkDelete}
                    disabled={selectedQuizzes.length === 0}
                  />
                </div>
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setSelectedQuizzes([])}
                className="glass hover:bg-white/20 dark:hover:bg-gray-800/20"
              >
                Clear Selection
              </Button>
            </div>
          </motion.div>
        )}

      {/* Quiz List */}
      {error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <AlertTriangle className="h-16 w-16 text-destructive mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Failed to load quizzes</h3>
              <p className="text-muted-foreground mb-6">
                {error}
              </p>
              <div className="flex items-center justify-center gap-3">
                <Button onClick={fetchQuizzes}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/admin/quizzes/create">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Quiz Instead
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : loading ? (
        <div className="grid gap-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="h-4 bg-muted rounded w-1/3"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                  <div className="h-3 bg-muted rounded w-1/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : quizzes.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              {searchQuery || typeFilter !== 'all' || statusFilter !== 'all' || difficultyFilter !== 'all' ? (
                // Filtered empty state
                <>
                  <h3 className="text-xl font-semibold mb-2">No quizzes match your filters</h3>
                  <p className="text-muted-foreground mb-6">
                    {searchQuery && `No quizzes found for "${searchQuery}". `}
                    {typeFilter !== 'all' && `Showing only ${typeFilter.toLowerCase().replace('_', ' ')} quizzes. `}
                    {statusFilter !== 'all' && `Showing only ${statusFilter} quizzes. `}
                    {difficultyFilter !== 'all' && `Showing only ${difficultyFilter.toLowerCase()} difficulty quizzes. `}
                    <br />
                    Try adjusting your search criteria or clearing filters.
                  </p>
                  <div className="flex items-center justify-center gap-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSearchQuery('')
                        setTypeFilter('all')
                        setStatusFilter('all')
                        setDifficultyFilter('all')
                        setCurrentPage(1)
                      }}
                    >
                      Clear Filters
                    </Button>
                    <Button asChild>
                      <Link href="/admin/quizzes/create">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Quiz
                      </Link>
                    </Button>
                  </div>
                </>
              ) : (
                // No quizzes at all
                <>
                  <h3 className="text-xl font-semibold mb-2">No quizzes created yet</h3>
                  <p className="text-muted-foreground mb-6">
                    Welcome to Quiz Management! Get started by creating your first quiz.
                    You can create different types of assessments including quizzes, test series, and daily practice sessions.
                  </p>
                  <div className="flex items-center justify-center gap-3">
                    <Button asChild>
                      <Link href="/admin/quizzes/create">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Your First Quiz
                      </Link>
                    </Button>
                    <Button variant="outline" asChild>
                      <Link href="/admin/help">
                        <FileText className="h-4 w-4 mr-2" />
                        View Guide
                      </Link>
                    </Button>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {/* Select All Header */}
          <div className="flex items-center gap-3 px-4 py-2 bg-muted/50 rounded-lg">
            <Checkbox
              checked={selectedQuizzes.length === quizzes.length}
              onCheckedChange={handleSelectAll}
            />
            <span className="text-sm font-medium">
              Select All ({quizzes.length} quiz{quizzes.length !== 1 ? 'es' : ''})
            </span>
          </div>

          {/* Quiz Cards */}
          <div className="grid gap-6">
            {quizzes.map((quiz, index) => (
              <motion.div
                key={quiz.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                whileHover={{ y: -2, scale: 1.01 }}
                className="group"
              >
                <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden relative">
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  <CardContent className="pt-6 relative z-10">
                    <div className="flex items-start gap-6">
                      <div className="flex items-center gap-4">
                        <Checkbox
                          checked={selectedQuizzes.includes(quiz.id)}
                          onCheckedChange={() => handleSelectQuiz(quiz.id)}
                          className="w-5 h-5 rounded-lg border-2 border-blue-300 dark:border-blue-600 data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-blue-500 data-[state=checked]:to-purple-600 data-[state=checked]:border-0"
                        />
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-xl backdrop-blur-sm border border-white/20 dark:border-gray-700/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                          <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                        </div>
                      </div>

                      <div className="flex-1 space-y-4">
                        <div className="space-y-3">
                          <div className="flex items-center gap-3 flex-wrap">
                            <h3 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                              {quiz.title}
                            </h3>
                            <Badge className={`${getStatusColor(quiz.isPublished)} border-0 shadow-sm`}>
                              {quiz.isPublished ? 'Published' : 'Draft'}
                            </Badge>
                            <Badge variant="outline" className={`${getDifficultyColor(quiz.difficulty)} border-2 font-semibold`}>
                              {quiz.difficulty}
                            </Badge>
                            {quiz.tags.length > 0 && (
                              <div className="flex gap-2">
                                {quiz.tags.slice(0, 2).map((tag) => (
                                  <Badge key={tag} className="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 border-0 text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                                {quiz.tags.length > 2 && (
                                  <Badge className="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 border-0 text-xs">
                                    +{quiz.tags.length - 2}
                                  </Badge>
                                )}
                              </div>
                            )}
                          </div>
                        </div>

                        {quiz.description && (
                          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                            {quiz.description}
                          </p>
                        )}

                        <div className="flex items-center gap-6 text-sm text-muted-foreground mb-3">
                          <div className="flex items-center gap-1">
                            <FileText className="h-4 w-4" />
                            <span>{quiz.questionCount} questions</span>
                          </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          <span>{quiz.attemptCount} attempts</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>Created {new Date(quiz.createdAt).toLocaleDateString()}</span>
                        </div>
                        {quiz.timeLimit && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>{quiz.timeLimit} min</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-4">
                        <div className="text-sm">
                          <span className="font-medium">Type:</span> {quiz.type.replace('_', ' ')}
                        </div>
                        <div className="text-sm">
                          <span className="font-medium">Avg Score:</span> {quiz.averageScore}%
                        </div>
                        <div className="text-sm">
                          <span className="font-medium">Created by:</span> {quiz.creator.name}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/admin/quizzes/${quiz.id}`}>
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/admin/quizzes/${quiz.id}/edit`}>
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Link>
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/admin/quizzes/${quiz.id}/preview`}>
                              <Eye className="h-4 w-4 mr-2" />
                              Preview
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDuplicateQuiz(quiz.id)}>
                            <Copy className="h-4 w-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleTogglePublish(quiz.id, quiz.isPublished)}
                          >
                            {quiz.isPublished ? (
                              <>
                                <Lock className="h-4 w-4 mr-2" />
                                Unpublish
                              </>
                            ) : (
                              <>
                                <Globe className="h-4 w-4 mr-2" />
                                Publish
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/admin/quizzes/${quiz.id}/analytics`}>
                              <BarChart3 className="h-4 w-4 mr-2" />
                              Analytics
                            </Link>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>

                      <DeleteConfirmationDialog
                        trigger={
                          <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        }
                        itemName={quiz.title}
                        itemType="quiz"
                        onDelete={() => handleDeleteQuiz(quiz.id)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Pagination */}
      {!loading && quizzes.length > 0 && totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                  >
                    {page}
                  </Button>
                )
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Stats Summary */}
      {!loading && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">{quizzes.length}</div>
              <p className="text-xs text-muted-foreground">Quizzes Shown</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">
                {quizzes.filter(q => q.isPublished).length}
              </div>
              <p className="text-xs text-muted-foreground">Published</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">
                {quizzes.filter(q => !q.isPublished).length}
              </div>
              <p className="text-xs text-muted-foreground">Drafts</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-2xl font-bold">
                {quizzes.length > 0
                  ? Math.round(quizzes.reduce((sum, q) => sum + q.averageScore, 0) / quizzes.length)
                  : 0}%
              </div>
              <p className="text-xs text-muted-foreground">Avg Score</p>
            </CardContent>
          </Card>
        </div>
      )}
      </motion.div>
    </div>
  )
}
