"use client"

import { useState, useEffect, use } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Clock, 
  Users, 
  Trophy,
  Play,
  Pause,
  Square,
  ArrowLeft,
  Zap,
  Target,
  CheckCircle,
  AlertCircle,
  Crown
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { useRouter } from "next/navigation"
import { StudentLayout } from "@/components/student/student-layout"
import { LiveQuizQuestion } from "@/components/student/live-quiz/live-quiz-question"
import { LiveQuizLeaderboard } from "@/components/student/live-quiz/live-quiz-leaderboard"
import { LiveQuizWaiting } from "@/components/student/live-quiz/live-quiz-waiting"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { getSocketClient } from "@/lib/socket-client"

interface LiveQuizSessionData {
  session: {
    id: string
    title: string
    status: 'WAITING' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED'
    currentQuestion: number
    questionTimeLimit?: number
    showLeaderboard: boolean
  }
  quiz: {
    id: string
    title: string
    questionCount: number
  }
  userParticipation: {
    id: string
    currentQuestion: number
    score: number
    correctAnswers: number
    totalAnswered: number
    rank?: number
    timeSpent: number
  } | null
  isParticipating: boolean
  canJoin: boolean
  participantCount: number
  currentQuestionData: any
  leaderboard: Array<{
    userId: string
    userName: string
    score: number
    rank: number
  }>
  stats: {
    totalParticipants: number
    averageScore: number
    questionCount: number
    progress: number
  }
}

export default function LiveQuizSession({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const sessionId = resolvedParams.id
  const router = useRouter()
  
  const [sessionData, setSessionData] = useState<LiveQuizSessionData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null)
  const [socketConnected, setSocketConnected] = useState(false)

  useEffect(() => {
    fetchSessionData()
    setupSocketConnection()
    
    return () => {
      cleanupSocketConnection()
    }
  }, [sessionId])

  useEffect(() => {
    // Timer for question time limit
    if (timeRemaining !== null && timeRemaining > 0) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev === null || prev <= 1) {
            return null
          }
          return prev - 1
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [timeRemaining])

  const fetchSessionData = async () => {
    try {
      const response = await fetch(`/api/student/live-quiz/sessions/${sessionId}`)
      const data = await response.json()

      if (data.success) {
        setSessionData(data.data)
        
        // Set timer if question has time limit and session is active
        if (data.data.session.status === 'ACTIVE' && 
            data.data.session.questionTimeLimit && 
            data.data.currentQuestionData) {
          setTimeRemaining(data.data.session.questionTimeLimit)
        }
      } else {
        setError(data.message || 'Failed to load session')
      }
    } catch (error) {
      console.error('Error fetching session data:', error)
      setError('Failed to load session')
    } finally {
      setLoading(false)
    }
  }

  const setupSocketConnection = () => {
    const socketClient = getSocketClient()
    
    // Join the live quiz session room
    socketClient.joinLiveQuizSession(sessionId)
    
    // Listen for session events
    socketClient.on('live-quiz:session-started', (data: any) => {
      if (data.sessionId === sessionId) {
        toast.success('Quiz has started!')
        fetchSessionData()
      }
    })

    socketClient.on('live-quiz:session-paused', (data: any) => {
      if (data.sessionId === sessionId) {
        toast.info('Quiz has been paused')
        fetchSessionData()
      }
    })

    socketClient.on('live-quiz:session-resumed', (data: any) => {
      if (data.sessionId === sessionId) {
        toast.success('Quiz has resumed!')
        fetchSessionData()
      }
    })

    socketClient.on('live-quiz:session-completed', (data: any) => {
      if (data.sessionId === sessionId) {
        toast.success('Quiz completed!')
        fetchSessionData()
      }
    })

    socketClient.on('live-quiz:question-changed', (data: any) => {
      if (data.sessionId === sessionId) {
        toast.info(`New question: ${data.questionData?.text?.substring(0, 50)}...`)
        fetchSessionData()
        
        // Reset timer for new question
        if (data.session.questionTimeLimit) {
          setTimeRemaining(data.session.questionTimeLimit)
        }
      }
    })

    socketClient.on('live-quiz:answer-feedback', (data: any) => {
      if (data.sessionId === sessionId) {
        if (data.isCorrect) {
          toast.success(`Correct! +${data.pointsEarned} points`)
        } else {
          toast.error('Incorrect answer')
        }
        fetchSessionData()
      }
    })

    socketClient.on('live-quiz:participant-joined', (data: any) => {
      if (data.sessionId === sessionId) {
        fetchSessionData()
      }
    })

    socketClient.on('live-quiz:participant-left', (data: any) => {
      if (data.sessionId === sessionId) {
        fetchSessionData()
      }
    })

    socketClient.on('connect', () => {
      setSocketConnected(true)
    })

    socketClient.on('disconnect', () => {
      setSocketConnected(false)
    })
  }

  const cleanupSocketConnection = () => {
    const socketClient = getSocketClient()
    socketClient.leaveLiveQuizSession(sessionId)
    socketClient.off('live-quiz:session-started')
    socketClient.off('live-quiz:session-paused')
    socketClient.off('live-quiz:session-resumed')
    socketClient.off('live-quiz:session-completed')
    socketClient.off('live-quiz:question-changed')
    socketClient.off('live-quiz:answer-feedback')
    socketClient.off('live-quiz:participant-joined')
    socketClient.off('live-quiz:participant-left')
  }

  const handleJoinSession = async () => {
    try {
      const response = await fetch(`/api/student/live-quiz/sessions/${sessionId}/join`, {
        method: 'POST'
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Successfully joined the quiz!')
        fetchSessionData()
      } else {
        throw new Error(data.message || 'Failed to join session')
      }
    } catch (error) {
      console.error('Error joining session:', error)
      toast.error('Failed to join session')
    }
  }

  const handleLeaveSession = async () => {
    try {
      const response = await fetch(`/api/student/live-quiz/sessions/${sessionId}/leave`, {
        method: 'POST'
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Left the quiz session')
        router.push('/student/live-quiz')
      } else {
        throw new Error(data.message || 'Failed to leave session')
      }
    } catch (error) {
      console.error('Error leaving session:', error)
      toast.error('Failed to leave session')
    }
  }

  const handleAnswerSubmit = async (questionId: string, answer: any, timeSpent: number) => {
    try {
      const response = await fetch(`/api/student/live-quiz/sessions/${sessionId}/answer`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          questionId,
          answer,
          timeSpent
        })
      })

      const data = await response.json()

      if (data.success) {
        // Socket will handle the feedback notification
        return data.data
      } else {
        throw new Error(data.message || 'Failed to submit answer')
      }
    } catch (error) {
      console.error('Error submitting answer:', error)
      toast.error('Failed to submit answer')
      return null
    }
  }

  if (loading) {
    return (
      <StudentLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading live quiz session...</p>
          </div>
        </div>
      </StudentLayout>
    )
  }

  if (error || !sessionData) {
    return (
      <StudentLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="max-w-md">
            <CardContent className="pt-6 text-center">
              <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Session Not Found</h3>
              <p className="text-muted-foreground mb-4">
                {error || 'The live quiz session could not be found or is no longer available.'}
              </p>
              <Button onClick={() => router.push('/student/live-quiz')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Live Quizzes
              </Button>
            </CardContent>
          </Card>
        </div>
      </StudentLayout>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'WAITING': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'ACTIVE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'PAUSED': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'COMPLETED': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getProgress = () => {
    if (sessionData.quiz.questionCount === 0) return 0
    return Math.round(((sessionData.session.currentQuestion + 1) / sessionData.quiz.questionCount) * 100)
  }

  return (
    <StudentLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/student/live-quiz')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{sessionData.session.title}</h1>
              <p className="text-muted-foreground">{sessionData.quiz.title}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getStatusColor(sessionData.session.status)}>
              {sessionData.session.status}
            </Badge>
            <Badge variant={socketConnected ? "default" : "destructive"}>
              {socketConnected ? "Connected" : "Disconnected"}
            </Badge>
          </div>
        </div>

        {/* Session Info */}
        <Card className="glass">
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{sessionData.participantCount}</div>
                <p className="text-sm text-muted-foreground">Participants</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {sessionData.session.currentQuestion + 1}/{sessionData.quiz.questionCount}
                </div>
                <p className="text-sm text-muted-foreground">Question</p>
              </div>
              {sessionData.userParticipation && (
                <>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{sessionData.userParticipation.score}</div>
                    <p className="text-sm text-muted-foreground">Your Score</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold flex items-center justify-center gap-1">
                      {sessionData.userParticipation.rank && (
                        <>
                          {sessionData.userParticipation.rank === 1 && <Crown className="h-5 w-5 text-yellow-500" />}
                          #{sessionData.userParticipation.rank}
                        </>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">Your Rank</p>
                  </div>
                </>
              )}
            </div>
            
            {/* Progress Bar */}
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-2">
                <span>Quiz Progress</span>
                <span>{getProgress()}%</span>
              </div>
              <Progress value={getProgress()} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            {sessionData.session.status === 'WAITING' && (
              <LiveQuizWaiting
                session={sessionData.session}
                quiz={sessionData.quiz}
                isParticipating={sessionData.isParticipating}
                canJoin={sessionData.canJoin}
                onJoin={handleJoinSession}
                onLeave={handleLeaveSession}
              />
            )}

            {sessionData.session.status === 'ACTIVE' && sessionData.isParticipating && sessionData.currentQuestionData && (
              <LiveQuizQuestion
                question={sessionData.currentQuestionData}
                sessionId={sessionId}
                timeRemaining={timeRemaining}
                onAnswerSubmit={handleAnswerSubmit}
                userParticipation={sessionData.userParticipation}
                onTimeUp={() => setTimeRemaining(null)}
              />
            )}

            {sessionData.session.status === 'ACTIVE' && !sessionData.isParticipating && (
              <Card className="glass">
                <CardContent className="pt-6 text-center">
                  <Zap className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">Quiz in Progress</h3>
                  <p className="text-muted-foreground mb-4">
                    This quiz is currently active. {sessionData.canJoin ? 'You can still join!' : 'Late joining is not allowed.'}
                  </p>
                  {sessionData.canJoin && (
                    <Button onClick={handleJoinSession}>
                      <Play className="h-4 w-4 mr-2" />
                      Join Now
                    </Button>
                  )}
                </CardContent>
              </Card>
            )}

            {sessionData.session.status === 'PAUSED' && (
              <Card className="glass">
                <CardContent className="pt-6 text-center">
                  <Pause className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">Quiz Paused</h3>
                  <p className="text-muted-foreground">
                    The quiz has been paused by the instructor. Please wait for it to resume.
                  </p>
                </CardContent>
              </Card>
            )}

            {sessionData.session.status === 'COMPLETED' && (
              <Card className="glass">
                <CardContent className="pt-6 text-center">
                  <Trophy className="h-12 w-12 mx-auto mb-4 text-primary" />
                  <h3 className="text-lg font-semibold mb-2">Quiz Completed!</h3>
                  <p className="text-muted-foreground mb-4">
                    Thank you for participating in this live quiz session.
                  </p>
                  {sessionData.userParticipation && (
                    <div className="bg-muted rounded-lg p-4 mb-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="font-semibold">Final Score</div>
                          <div className="text-2xl font-bold text-primary">{sessionData.userParticipation.score}</div>
                        </div>
                        <div>
                          <div className="font-semibold">Final Rank</div>
                          <div className="text-2xl font-bold text-primary">
                            #{sessionData.userParticipation.rank || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <Button onClick={() => router.push('/student/live-quiz')}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Live Quizzes
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {sessionData.session.showLeaderboard && sessionData.leaderboard.length > 0 && (
              <LiveQuizLeaderboard
                leaderboard={sessionData.leaderboard}
                currentUserId={sessionData.userParticipation?.id}
              />
            )}

            {sessionData.isParticipating && (
              <Card className="glass">
                <CardHeader>
                  <CardTitle className="text-base">Session Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <ConfirmationDialog
                    trigger={
                      <Button variant="outline" className="w-full">
                        <Square className="h-4 w-4 mr-2" />
                        Leave Session
                      </Button>
                    }
                    title="Leave Live Quiz Session"
                    description="Are you sure you want to leave this live quiz session? Your progress will be saved but you may not be able to rejoin."
                    confirmText="Leave Session"
                    variant="destructive"
                    onConfirm={handleLeaveSession}
                  />
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </StudentLayout>
  )
}
